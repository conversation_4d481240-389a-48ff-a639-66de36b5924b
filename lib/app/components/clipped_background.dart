import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class ClippedBackground extends StatelessWidget {
  final Widget? child;
  final bool clipped;

  const ClippedBackground({
    Key? key,
    this.child,
    this.clipped = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (clipped == true) {
      return ClipRRect(
        borderRadius: kTopRadius,
        child: _child(context),
      );
    }
    return _child(context);
  }

  Widget _child(BuildContext context) {
    return SizedBox.expand(
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: kTopRadius,
          color: Theme.of(context).colorScheme.surface,
        ),
        child: child,
      ),
    );
  }
}
